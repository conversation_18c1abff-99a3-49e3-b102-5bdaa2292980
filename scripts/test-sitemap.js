#!/usr/bin/env node

/**
 * Script to test sitemap generation and validate URLs
 * Run with: node scripts/test-sitemap.js
 */

const https = require('https')
const http = require('http')

const baseUrl = 'https://geminigen.ai'

// URLs to test from sitemap
const testUrls = [
  '/',
  '/app',
  '/app/video-gen',
  '/app/speech-gen',
  '/app/dialogue-gen',
  '/app/imagen',
  '/app/music-gen',
  '/pricing',
  '/public',
  '/public/thank-you',
  '/auth/login',
  '/auth/signup',
  '/auth/account-recovery',
  '/auth/reset-password',
  '/terms',
  '/privacy'
]

// Sitemap URLs to test
const sitemapUrls = [
  '/sitemap-index.xml',
  '/sitemap.xml',
  '/sitemap-docs.xml',
  '/sitemap-blog.xml'
]

function testUrl(url) {
  return new Promise((resolve) => {
    const fullUrl = `${baseUrl}${url}`
    const client = fullUrl.startsWith('https') ? https : http
    
    const req = client.get(fullUrl, (res) => {
      resolve({
        url,
        status: res.statusCode,
        success: res.statusCode >= 200 && res.statusCode < 400
      })
    })
    
    req.on('error', (err) => {
      resolve({
        url,
        status: 'ERROR',
        success: false,
        error: err.message
      })
    })
    
    req.setTimeout(10000, () => {
      req.destroy()
      resolve({
        url,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      })
    })
  })
}

async function testSitemap() {
  console.log('🔍 Testing sitemap URLs...\n')
  
  // Test sitemap files
  console.log('📄 Testing sitemap files:')
  for (const url of sitemapUrls) {
    const result = await testUrl(url)
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${url} - ${result.status}`)
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
  }
  
  console.log('\n📋 Testing page URLs:')
  
  // Test page URLs
  const results = []
  for (const url of testUrls) {
    const result = await testUrl(url)
    results.push(result)
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${url} - ${result.status}`)
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
  }
  
  // Summary
  const successful = results.filter(r => r.success).length
  const total = results.length
  
  console.log('\n📊 Summary:')
  console.log(`✅ Successful: ${successful}/${total}`)
  console.log(`❌ Failed: ${total - successful}/${total}`)
  
  if (successful === total) {
    console.log('\n🎉 All URLs are accessible!')
  } else {
    console.log('\n⚠️  Some URLs failed. Please check the errors above.')
  }
}

// Validate sitemap XML structure
function validateSitemapXML(xmlContent) {
  const requiredElements = [
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"',
    '<url>',
    '<loc>',
    '<lastmod>',
    '<changefreq>',
    '<priority>'
  ]
  
  const issues = []
  
  requiredElements.forEach(element => {
    if (!xmlContent.includes(element)) {
      issues.push(`Missing required element: ${element}`)
    }
  })
  
  return {
    valid: issues.length === 0,
    issues
  }
}

console.log('🚀 GeminiGen AI Sitemap Tester')
console.log('================================\n')

testSitemap().catch(console.error)
