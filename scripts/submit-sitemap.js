#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to submit sitemap to search engines
 * Run with: node scripts/submit-sitemap.js
 */

const https = require('https')

const baseUrl = 'https://geminigen.ai'
const sitemapUrl = `${baseUrl}/sitemap-index.xml`

// Search engine ping URLs
const searchEngines = [
  {
    name: 'Google',
    url: `https://www.google.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
  },
  {
    name: 'Bing',
    url: `https://www.bing.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`
  }
]

function pingSearchEngine(engine) {
  return new Promise((resolve) => {
    console.log(`📡 Pinging ${engine.name}...`)
    
    const req = https.get(engine.url, (res) => {
      const success = res.statusCode >= 200 && res.statusCode < 300
      resolve({
        engine: engine.name,
        status: res.statusCode,
        success
      })
    })
    
    req.on('error', (err) => {
      resolve({
        engine: engine.name,
        status: 'ERROR',
        success: false,
        error: err.message
      })
    })
    
    req.setTimeout(10000, () => {
      req.destroy()
      resolve({
        engine: engine.name,
        status: 'TIMEOUT',
        success: false,
        error: 'Request timeout'
      })
    })
  })
}

async function submitSitemap() {
  console.log('🚀 Submitting sitemap to search engines')
  console.log(`📍 Sitemap URL: ${sitemapUrl}\n`)
  
  const results = []
  
  for (const engine of searchEngines) {
    const result = await pingSearchEngine(engine)
    results.push(result)
    
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.engine}: ${result.status}`)
    
    if (result.error) {
      console.log(`   Error: ${result.error}`)
    }
    
    // Add delay between requests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('\n📊 Summary:')
  const successful = results.filter(r => r.success).length
  console.log(`✅ Successful: ${successful}/${results.length}`)
  console.log(`❌ Failed: ${results.length - successful}/${results.length}`)
  
  if (successful > 0) {
    console.log('\n🎉 Sitemap submitted successfully!')
    console.log('\n📝 Next steps:')
    console.log('1. Verify sitemap in Google Search Console')
    console.log('2. Monitor indexing status')
    console.log('3. Check for any crawl errors')
  } else {
    console.log('\n⚠️  All submissions failed. Please check your sitemap URL and try again.')
  }
}

// Additional sitemap validation
function validateSitemapUrl() {
  return new Promise((resolve) => {
    console.log('🔍 Validating sitemap URL...')
    
    const req = https.get(sitemapUrl, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        const isValid = res.statusCode === 200 && data.includes('<?xml') && data.includes('<sitemapindex')
        resolve({
          valid: isValid,
          status: res.statusCode,
          hasXml: data.includes('<?xml'),
          hasSitemapIndex: data.includes('<sitemapindex')
        })
      })
    })
    
    req.on('error', (err) => {
      resolve({
        valid: false,
        error: err.message
      })
    })
    
    req.setTimeout(10000, () => {
      req.destroy()
      resolve({
        valid: false,
        error: 'Request timeout'
      })
    })
  })
}

async function main() {
  console.log('🗺️  GeminiGen AI Sitemap Submitter')
  console.log('==================================\n')
  
  // First validate the sitemap
  const validation = await validateSitemapUrl()
  
  if (validation.valid) {
    console.log('✅ Sitemap validation passed\n')
    await submitSitemap()
  } else {
    console.log('❌ Sitemap validation failed')
    console.log(`Status: ${validation.status}`)
    if (validation.error) {
      console.log(`Error: ${validation.error}`)
    }
    console.log('\nPlease fix the sitemap before submitting.')
    process.exit(1)
  }
}

main().catch(console.error)
