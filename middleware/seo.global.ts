export default defineNuxtRouteMiddleware((to) => {
  // Skip SEO middleware for API routes and static assets
  if (to.path.startsWith('/api/') || to.path.startsWith('/_nuxt/')) {
    return
  }

  // Auto-apply SEO based on route
  const { setPageSEO, setBreadcrumbs } = useSEO()

  // Define route-specific SEO configurations
  const seoConfigs: Record<string, any> = {
    '/': {
      title: 'Save on Veo 3 & Imagen APIs | AI Video & Image | GeminiGen.ai',
      description: 'Create stunning AI-generated content with GeminiGen AI. Advanced tools for video generation, speech synthesis, dialogue creation, and image generation.',
      keywords: 'AI video generation, AI speech synthesis, AI image generation, artificial intelligence, content creation'
    },
    '/app': {
      title: 'AI Tools Dashboard - GeminiGen AI',
      description: 'Access all AI tools in one place. Generate videos, create speech, design images, and more with our comprehensive AI platform.',
      keywords: 'AI tools, AI dashboard, video generation, speech synthesis, image creation'
    },
    '/app/video-gen': {
      title: 'AI Video Generator - Create Videos from Text | GeminiGen AI',
      description: 'Generate high-quality AI videos from text prompts using advanced Veo models. Create professional videos and animations with AI.',
      keywords: 'AI video generator, text to video, video creation, Veo models, AI animation'
    },
    '/app/speech-gen': {
      title: 'AI Speech Generator - Text to Speech | GeminiGen AI',
      description: 'Convert text to natural-sounding speech with our AI voice generator. Multiple voices, languages, and customization options.',
      keywords: 'text to speech, AI voice generator, speech synthesis, voice cloning, TTS'
    },
    '/app/dialogue-gen': {
      title: 'AI Dialogue Generator - Create Conversations | GeminiGen AI',
      description: 'Generate realistic dialogues and conversations with AI. Perfect for scripts, stories, and interactive content.',
      keywords: 'AI dialogue generator, conversation AI, script writing, dialogue creation'
    },
    '/app/imagen': {
      title: 'AI Image Generator - Create Images from Text | GeminiGen AI',
      description: 'Generate stunning images from text descriptions using advanced AI models. Create art, photos, and designs instantly.',
      keywords: 'AI image generator, text to image, AI art, image creation, digital art'
    },
    '/app/music-gen': {
      title: 'AI Music Generator - Create Music with AI | GeminiGen AI',
      description: 'Compose original music and soundtracks with AI. Generate melodies, beats, and full compositions instantly.',
      keywords: 'AI music generator, music composition, AI composer, soundtrack creation'
    },
    '/pricing': {
      title: 'Pricing - Affordable AI Content Generation Plans | GeminiGen AI',
      description: 'Simple and flexible pricing for AI content generation. Pay only for what you use with our credit-based system.',
      keywords: 'AI pricing, content generation cost, AI tools pricing, subscription plans'
    },
    '/auth/login': {
      title: 'Login - Access Your AI Tools | GeminiGen AI',
      description: 'Sign in to your GeminiGen AI account to access all AI tools and manage your content generation projects.',
      keywords: 'login, sign in, AI tools access, user account'
    },
    '/auth/signup': {
      title: 'Sign Up - Start Creating with AI | GeminiGen AI',
      description: 'Create your free GeminiGen AI account and start generating amazing content with our AI tools today.',
      keywords: 'sign up, register, create account, free AI tools'
    },
    '/terms': {
      title: 'Terms of Service - GeminiGen AI',
      description: 'Read our terms of service and user agreement for using GeminiGen AI platform and services.',
      keywords: 'terms of service, user agreement, legal terms'
    },
    '/privacy': {
      title: 'Privacy Policy - GeminiGen AI',
      description: 'Learn how we protect your privacy and handle your data on the GeminiGen AI platform.',
      keywords: 'privacy policy, data protection, user privacy'
    }
  }

  // Apply SEO configuration if available
  const config = seoConfigs[to.path]
  if (config) {
    setPageSEO(config)
  }

  // Set breadcrumbs for nested routes
  if (to.path.startsWith('/app/')) {
    const breadcrumbs = [
      { name: 'Home', url: '/' },
      { name: 'AI Tools', url: '/app' }
    ]

    // Add specific tool breadcrumb
    if (to.path === '/app/video-gen') {
      breadcrumbs.push({ name: 'Video Generator', url: '/app/video-gen' })
    } else if (to.path === '/app/speech-gen') {
      breadcrumbs.push({ name: 'Speech Generator', url: '/app/speech-gen' })
    } else if (to.path === '/app/dialogue-gen') {
      breadcrumbs.push({ name: 'Dialogue Generator', url: '/app/dialogue-gen' })
    } else if (to.path === '/app/imagen') {
      breadcrumbs.push({ name: 'Image Generator', url: '/app/imagen' })
    } else if (to.path === '/app/music-gen') {
      breadcrumbs.push({ name: 'Music Generator', url: '/app/music-gen' })
    }

    setBreadcrumbs(breadcrumbs)
  }
})
