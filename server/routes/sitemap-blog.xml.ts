export default defineEventHandler(async (event) => {
  const baseUrl = 'https://geminigen.ai'
  const currentDate = new Date().toISOString().split('T')[0]

  // Define blog routes - add your actual blog posts here
  const blogRoutes = [
    {
      url: '/blog',
      lastmod: currentDate,
      changefreq: 'weekly',
      priority: '0.8'
    }
    // Add individual blog posts here when available
    // {
    //   url: '/blog/how-to-use-ai-video-generation',
    //   lastmod: '2024-01-15',
    //   changefreq: 'monthly',
    //   priority: '0.7'
    // },
    // {
    //   url: '/blog/best-practices-for-ai-speech-generation',
    //   lastmod: '2024-01-10',
    //   changefreq: 'monthly',
    //   priority: '0.7'
    // }
  ]

  // Generate XML sitemap
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${blogRoutes.map(route => `  <url>
    <loc>${baseUrl}${route.url}</loc>
    <lastmod>${route.lastmod}</lastmod>
    <changefreq>${route.changefreq}</changefreq>
    <priority>${route.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  // Set proper headers
  setHeader(event, 'Content-Type', 'application/xml')
  setHeader(event, 'Cache-Control', 'max-age=3600') // Cache for 1 hour

  return sitemap
})
