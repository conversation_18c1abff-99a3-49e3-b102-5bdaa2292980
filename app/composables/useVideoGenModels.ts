export function useVideoGenModels() {
  const models = [
    {
      label: 'Veo 3 Fast',
      value: 'veo-3-fast',
      icon: 'hugeicons:ai-chip',
      options: ['style', 'videoDimensions', 'yourImage', 'resolution'],
      ratios: ['16:9', '9:16'],
      enhancePrompt: {
        default: true,
        locked: true
      },
      duration: {
        options: [8],
        default: 8
      },
      resolutions: {
        options: ['720p', '1080p'],
        default: '720p'
      }
    },
    {
      label: 'Veo 3',
      value: 'veo-3',
      icon: 'hugeicons:ai-chip',
      options: ['style', 'videoDimensions', 'yourImage', 'resolution'],
      ratios: ['16:9', '9:16'],
      enhancePrompt: {
        default: true,
        locked: true
      },
      duration: {
        options: [8],
        default: 8
      },
      resolutions: {
        options: ['720p', '1080p'],
        default: '720p'
      }
    },

    {
      label: 'Veo 2',
      value: 'veo-2',
      icon: 'hugeicons:ai-chip',
      options: ['style', 'videoDimensions', 'yourImage', 'duration'],
      ratios: ['16:9', '9:16'],
      enhancePrompt: {
        default: true,
        locked: false
      },
      duration: {
        options: [5, 6, 7, 8],
        default: 8
      }
    }
  ]
  const model = useCookie<any>('video-gen-model', { default: () => models[0] })

  return {
    models,
    model
  }
}
