export function useServerSelection() {
  // Server selection state
  const selectedServer = useCookie<string>('server-selection', {
    default: () => 'regular'
  })

  // Server options
  const serverOptions = computed(() => [
    {
      label: 'Regular Server',
      value: 'regular',
      icon: 'lucide:server',
      description_c: 'Reliable but may be slower at peak times',
      badge: 'Promotional Price',
      badgeColor: 'green' as const
    },
    {
      label: 'VIP Server',
      value: 'vip',
      icon: 'lucide:crown',
      description_c: 'Smooth, priority performance, no delays',
      badge: 'Premium',
      badgeColor: 'amber' as const
    }
  ])

  // Check if server selection feature is enabled
  const isServerSelectionEnabled = computed(() => {
    return canUseFeature('serverSelection')
  })

  // Convert to service_mode for API calls
  const serviceMode = computed(() => {
    return selectedServer.value === 'vip' && isServerSelectionEnabled.value ? 'stable' : 'unstable'
  })

  // Check if current selection is VIP
  const isVipServer = computed(() => {
    return selectedServer.value === 'vip' && isServerSelectionEnabled.value
  })

  // Check if current selection is regular
  const isRegularServer = computed(() => {
    return selectedServer.value === 'regular'
  })

  return {
    selectedServer,
    serverOptions,
    isServerSelectionEnabled,
    serviceMode,
    isVipServer,
    isRegularServer
  }
}
