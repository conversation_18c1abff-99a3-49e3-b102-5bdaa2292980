import { describe, it, expect } from 'vitest'
import { validateUsername, hasValidUsernameCharacters, sanitizeUsername, USERNAME_REGEX } from '../usernameValidation'

describe('usernameValidation', () => {
  describe('validateUsername', () => {
    it('should validate correct usernames', () => {
      const validUsernames = [
        'john_doe',
        'user123',
        'test-user',
        'a1',
        'user_name_123',
        'test-123-user',
        'JohnDoe',
        'UPPERCASE',
        'lowercase'
      ]

      validUsernames.forEach(username => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(true)
        expect(result.message).toBeUndefined()
      })
    })

    it('should reject usernames with special characters', () => {
      const invalidUsernames = [
        '<EMAIL>',
        'user name',
        'user!',
        'user#123',
        'user$',
        'user%',
        'user&',
        'user*',
        'user+',
        'user=',
        'user?',
        'user.',
        'user,',
        'user;',
        'user:',
        'user/',
        'user\\',
        'user|',
        'user<>',
        'user[]',
        'user{}',
        'user()',
        'user"',
        "user'",
        'user`',
        'user~',
        'user^'
      ]

      invalidUsernames.forEach(username => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(false)
        expect(result.message).toBeDefined()
      })
    })

    it('should reject usernames that are too short', () => {
      const result = validateUsername('a')
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('2 characters')
    })

    it('should reject usernames that are too long', () => {
      const longUsername = 'a'.repeat(51)
      const result = validateUsername(longUsername)
      expect(result.isValid).toBe(false)
      expect(result.message).toContain('50 characters')
    })

    it('should reject empty or whitespace usernames', () => {
      const emptyUsernames = ['', '   ', '\t', '\n']

      emptyUsernames.forEach(username => {
        const result = validateUsername(username)
        expect(result.isValid).toBe(false)
        expect(result.message).toBeDefined()
      })
    })

    it('should trim whitespace from usernames', () => {
      const result = validateUsername('  valid_user  ')
      expect(result.isValid).toBe(true)
    })
  })

  describe('hasValidUsernameCharacters', () => {
    it('should return true for valid character combinations', () => {
      expect(hasValidUsernameCharacters('abc123')).toBe(true)
      expect(hasValidUsernameCharacters('user_name')).toBe(true)
      expect(hasValidUsernameCharacters('test-user')).toBe(true)
      expect(hasValidUsernameCharacters('ABC')).toBe(true)
      expect(hasValidUsernameCharacters('123')).toBe(true)
      expect(hasValidUsernameCharacters('_')).toBe(true)
      expect(hasValidUsernameCharacters('-')).toBe(true)
    })

    it('should return false for invalid characters', () => {
      expect(hasValidUsernameCharacters('user@domain')).toBe(false)
      expect(hasValidUsernameCharacters('user name')).toBe(false)
      expect(hasValidUsernameCharacters('user!')).toBe(false)
      expect(hasValidUsernameCharacters('user#')).toBe(false)
    })
  })

  describe('sanitizeUsername', () => {
    it('should remove invalid characters', () => {
      expect(sanitizeUsername('<EMAIL>')).toBe('userdomaincom')
      expect(sanitizeUsername('user name!')).toBe('username')
      expect(sanitizeUsername('user#123$')).toBe('user123')
      expect(sanitizeUsername('test-user_123')).toBe('test-user_123')
    })

    it('should preserve valid characters', () => {
      expect(sanitizeUsername('valid_user-123')).toBe('valid_user-123')
      expect(sanitizeUsername('ABC123')).toBe('ABC123')
    })
  })

  describe('USERNAME_REGEX', () => {
    it('should match valid username patterns', () => {
      expect(USERNAME_REGEX.test('user123')).toBe(true)
      expect(USERNAME_REGEX.test('user_name')).toBe(true)
      expect(USERNAME_REGEX.test('test-user')).toBe(true)
    })

    it('should not match invalid username patterns', () => {
      expect(USERNAME_REGEX.test('user@domain')).toBe(false)
      expect(USERNAME_REGEX.test('user name')).toBe(false)
      expect(USERNAME_REGEX.test('user!')).toBe(false)
    })
  })
})
