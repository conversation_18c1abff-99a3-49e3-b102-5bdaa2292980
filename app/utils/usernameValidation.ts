import * as z from 'zod'

/**
 * Username validation regex - allows only letters, numbers, underscores, and hyphens
 */
export const USERNAME_REGEX = /^[a-zA-Z0-9_-]+$/

/**
 * Creates a Zod schema for username validation with i18n support
 * @param t - i18n translation function
 * @returns Zod string schema with username validation rules
 */
export const createUsernameSchema = (t: (key: string) => string) => {
  return z
    .string({
      required_error: t('validation.required')
    })
    .min(1, t('validation.required'))
    .min(2, t('validation.usernameMinLength'))
    .max(50, t('validation.usernameMaxLength'))
    .regex(USERNAME_REGEX, t('validation.usernameInvalidCharacters'))
}

/**
 * Validates username format (no special characters except underscore and hyphen)
 * @param username - The username to validate
 * @param t - i18n translation function
 * @returns Validation result with isValid boolean and optional message
 */
export const validateUsername = (username: string, t?: (key: string) => string): { isValid: boolean; message?: string } => {
  if (!username || !username.trim()) {
    return { 
      isValid: false, 
      message: t ? t('validation.required') : 'Username is required' 
    }
  }

  const trimmedUsername = username.trim()
  
  if (trimmedUsername.length < 2) {
    return { 
      isValid: false, 
      message: t ? t('validation.usernameMinLength') : 'Username must be at least 2 characters long' 
    }
  }

  if (trimmedUsername.length > 50) {
    return { 
      isValid: false, 
      message: t ? t('validation.usernameMaxLength') : 'Username must be 50 characters or less' 
    }
  }

  if (!USERNAME_REGEX.test(trimmedUsername)) {
    return { 
      isValid: false, 
      message: t ? t('validation.usernameInvalidCharacters') : 'Username can only contain letters, numbers, underscores, and hyphens' 
    }
  }

  return { isValid: true }
}

/**
 * Checks if a string contains only valid username characters
 * @param str - String to check
 * @returns boolean indicating if string contains only valid characters
 */
export const hasValidUsernameCharacters = (str: string): boolean => {
  return USERNAME_REGEX.test(str)
}

/**
 * Sanitizes a string to contain only valid username characters
 * @param str - String to sanitize
 * @returns Sanitized string with only valid username characters
 */
export const sanitizeUsername = (str: string): string => {
  return str.replace(/[^a-zA-Z0-9_-]/g, '')
}
