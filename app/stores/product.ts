import { defineStore } from 'pinia'

interface Product {
  id: string
  name: string
  type: number
  max_quantity: number
  max_monthly_total_file: number
  max_file_size: number
  price_divide_100: number
  base_credit: number
  bonus_credit: number
  special_bonus_credit: number
  days_of_valid: number
  paypal_plan_id: string
  features: string[]
}

interface ServicePrice {
  currency: string
  description: string
  effective_price: number
  model_name: string
  original_price: number
  price_unit?: string
  service_mode: string
  resolution?: string
}

export const useProductStore = defineStore('productStore', {
  state: () => ({
    products: [] as Product[],
    servicePrices: [] as ServicePrice[],
    servicePricesLoading: false,
    servicePricesError: null as any,
    // Promotion system for 100% bonus credits
    promotionActive: true, // Always active as per requirement
    promotionBonusPercentage: 100 // 100% bonus
  }),
  getters: {
    buyCreditProduct: (state) => {
      return state.products.find(product => product.type === 2) || null
    },
    getServicePriceByModelName:
      state =>
        (modelName: string, service_mode = 'unstable') => {
          return state.servicePrices.find(
            price =>
              price.model_name === modelName
              && price.service_mode === service_mode
          )
        },
    getServicePriceByModelNameAndResolution:
      state =>
        (modelName: string, service_mode = 'unstable', resolution = '720p') => {
          console.log('🚀 ~ resolution:', resolution)
          console.log('🚀 ~ service_mode:', service_mode)
          console.log('🚀 ~ modelName:', modelName)
          return state.servicePrices.find(
            price =>
              price.model_name === modelName
              && price.service_mode === service_mode
              && price.resolution === resolution
          )
        },
    // Base credits without promotion (new rate: $10 = 1000 credits)
    oneUSDCreditsBase(): number {
      // New rate: $10 = 1000 credits, so $1 = 100 credits
      return 100
    },
    // For backward compatibility - returns base credits (without promotion)
    oneUSDCredits(): number {
      return this.oneUSDCreditsBase
    },
    // New credit calculation with promotion
    oneUSDCreditsWithPromotion(): number {
      const baseCredits = this.oneUSDCreditsBase
      if (this.promotionActive) {
        return baseCredits * (1 + this.promotionBonusPercentage / 100)
      }
      return baseCredits
    },
    // Check if promotion is active
    isPromotionActive(): boolean {
      return this.promotionActive
    },
    // Get promotion bonus percentage
    getPromotionBonusPercentage(): number {
      return this.promotionBonusPercentage
    }
  },
  actions: {
    async fetchProducts() {
      const { apiService } = useAPI()
      const response = await apiService.get('/products')
      const data = response.data
      if (data.success) {
        this.products = data.result
      }
      return this.products
    },

    async fetchServicePrices() {
      try {
        this.servicePricesLoading = true
        this.servicePricesError = null

        const { apiService } = useAPI()
        const response = await apiService.get('/service-prices')
        const data: ServicePrice[] = response.data

        if (data) {
          this.servicePrices = data
        }

        return data
      } catch (error: any) {
        console.error('🚀 ~ fetchServicePrices error:', error)
        this.servicePricesError
          = error.response?.data?.message || error.message
        return null
      } finally {
        this.servicePricesLoading = false
      }
    }
  }
})
