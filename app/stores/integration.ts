import { defineStore } from 'pinia'

interface ApiKey {
  id: string
  api_key: string
  name: string
  created_at: string
  updated_at: string
}

interface ApiKeyResponse {
  success: boolean
  result: ApiKey[]
}

interface CreateApiKeyRequest {
  name: string
}

interface DeleteApiKeyRequest {
  id: string
}

interface WebhookResponse {
  success: boolean
  result: {
    webhook_url: string
    created_at?: string
    updated_at?: string
  }
}

interface IntegrationState {
  apiKeys: ApiKey[]
  webhookUrl: string
  loadings: {
    fetchApiKeys: boolean
    createApiKey: boolean
    deleteApiKey: boolean
    saveWebhook: boolean
    fetchWebhook: boolean
  }
  errors: {
    fetchApiKeys: any
    createApiKey: any
    deleteApiKey: any
    saveWebhook: any
    fetchWebhook: any
  }
}

export const useIntegrationStore = defineStore('integrationStore', {
  state: (): IntegrationState => ({
    apiKeys: [],
    webhookUrl: '',
    loadings: {
      fetchApiKeys: false,
      createApiKey: false,
      deleteApiKey: false,
      saveWebhook: false,
      fetchWebhook: false
    },
    errors: {
      fetchApiKeys: null,
      createApiKey: null,
      deleteApiKey: null,
      saveWebhook: null,
      fetchWebhook: null
    }
  }),

  getters: {
    hasApiKeys: state => state.apiKeys.length > 0
  },

  actions: {
    async fetchApiKeys() {
      try {
        this.loadings.fetchApiKeys = true
        this.errors.fetchApiKeys = null

        const { apiService } = useAPI()
        const response = await apiService.get('/user/api-key')
        const data: ApiKeyResponse = (response as any).data || (response as any).body

        this.apiKeys = data.result || []

        return data
      } catch (error: any) {
        console.log('🚀 ~ fetchApiKeys error:', error)
        this.errors.fetchApiKeys = error.response?.data?.detail || error.message
        return null
      } finally {
        this.loadings.fetchApiKeys = false
      }
    },

    async createApiKey(payload: CreateApiKeyRequest) {
      try {
        this.loadings.createApiKey = true
        this.errors.createApiKey = null

        const { apiService } = useAPI()
        const response = await apiService.post('/user/api-key', payload)
        const data = (response as any).data || (response as any).body

        // Store the newly created API key data before refreshing
        const newApiKey = data.result || data

        // Refresh the API keys list after creation
        await this.fetchApiKeys()

        // Return the new API key data with full key visible
        return { ...data, result: newApiKey }
      } catch (error: any) {
        console.log('🚀 ~ createApiKey error:', error)
        this.errors.createApiKey = error.response?.data?.detail || error.message
        return null
      } finally {
        this.loadings.createApiKey = false
      }
    },

    async deleteApiKey(payload: DeleteApiKeyRequest) {
      try {
        this.loadings.deleteApiKey = true
        this.errors.deleteApiKey = null

        const { apiService } = useAPI()
        const response = await apiService.delete('/user/api-key', { data: payload })
        const data = (response as any).data || (response as any).body

        // Remove the API key from local state
        this.apiKeys = this.apiKeys.filter(key => key.id != payload.id)

        return data
      } catch (error: any) {
        console.log('🚀 ~ deleteApiKey error:', error)
        this.errors.deleteApiKey = error.response?.data?.detail || error.message
        return null
      } finally {
        this.loadings.deleteApiKey = false
      }
    },

    async fetchWebhook() {
      try {
        this.loadings.fetchWebhook = true
        this.errors.fetchWebhook = null

        const { apiService } = useAPI()
        const response = await apiService.get('/user/webhook')
        const data: WebhookResponse = (response as any).data || (response as any).body

        this.webhookUrl = data.result?.webhook_url || ''

        return data
      } catch (error: any) {
        console.log('🚀 ~ fetchWebhook error:', error)
        this.errors.fetchWebhook = error.response?.data?.detail || error.message
        // If webhook not found, just set empty string
        if (error.response?.status === 404) {
          this.webhookUrl = ''
        }
        return null
      } finally {
        this.loadings.fetchWebhook = false
      }
    },

    async saveWebhookUrl(url: string) {
      try {
        this.loadings.saveWebhook = true
        this.errors.saveWebhook = null

        const { apiService } = useAPI()
        const response = await apiService.post('/user/webhook', { webhook_url: url })
        const data = (response as any).data || (response as any).body

        // Update local state
        this.webhookUrl = url

        return data
      } catch (error: any) {
        console.log('🚀 ~ saveWebhookUrl error:', error)
        this.errors.saveWebhook = error.response?.data?.detail || error.message
        return null
      } finally {
        this.loadings.saveWebhook = false
      }
    }
  }
})
