// Define a store to manage logo state
import { defineStore } from 'pinia'

export const useTextToVideoStore = defineStore('textToVideoStore', {
  state: () => ({
    textToVideoResult: null as any,
    aiToolVideoCardRef: null as any,

    selectedImages: [] as any[],

    loadings: {
      textToVideo: false
    } as Record<string, boolean>,

    errors: {
      textToVideo: null
    } as Record<string, any>,
    prompt: '',
    negativePrompt: ''
  }),

  actions: {
    async textToVideo(payload: {
      prompt: string
      negative_prompt?: string
      model: string
      aspect_ratio: string
      enhance_prompt?: boolean
      duration?: number
      resolution?: string
      service_mode?: string
      files: File[]
    }) {
      const appStore = useAppStore()
      this.textToVideoResult = {
        model_name: payload.model,
        aspect_ratio: payload.aspect_ratio,
        enhance_prompt: payload.enhance_prompt,
        negative_prompt: payload.negative_prompt,
        files: payload.files,
        duration: payload.duration,
        resolution: payload.resolution
      }
      // appStore.loading = true
      const toast = useToast()

      try {
        this.loadings.textToVideo = true
        this.errors.textToVideo = null

        // Create FormData for multipart/form-data
        const formData = new FormData()

        // Add text fields
        formData.append('prompt', payload.prompt)
        formData.append('model', payload.model)
        formData.append('aspect_ratio', payload.aspect_ratio)

        if (payload.negative_prompt) {
          formData.append('negative_prompt', payload.negative_prompt)
        }

        if (payload.enhance_prompt !== undefined) {
          formData.append('enhance_prompt', payload.enhance_prompt.toString())
        }

        if (payload.duration) {
          formData.append('duration', payload.duration.toString())
        }

        if (payload.resolution) {
          formData.append('resolution', payload.resolution)
        }

        if (payload.service_mode) {
          formData.append('service_mode', payload.service_mode)
        }

        // Add files only if provided and not empty
        if (payload.files && payload.files.length > 0) {
          payload.files.forEach((file) => {
            formData.append('files', file)
          })
        }

        // Make the API call
        const { apiService } = useAPI()
        const response = await apiService.post('/video-gen/veo', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        console.log('🚀 ~ UUID:', response.data.uuid)
        this.textToVideoResult = {
          ...this.textToVideoResult,
          ...response.data
        }

        // Start auto sync if we have a history UUID
        if (response.data.uuid) {
          const { startAutoSync } = useAutoSyncHistory()
          console.log('🚀 ~ Starting auto sync for UUID:', response.data.uuid)
          startAutoSync({
            uuid: response.data.uuid,
            intervalMs: 30000, // 30 seconds
            maxDurationMs: 300000, // 5 minutes
            targetStatuses: [2, 3], // Complete or Error
            onStatusChange: (status, historyDetail) => {
              console.log(`🚀 ~ Video generation status update: ${status}`)
              // Update result with latest data
              this.textToVideoResult = {
                ...this.textToVideoResult,
                ...historyDetail
              }
            },
            onComplete: (historyDetail) => {
              console.log('🚀 ~ Video generation completed:', historyDetail)
              this.textToVideoResult = {
                ...this.textToVideoResult,
                ...historyDetail
              }
            },
            onError: (error) => {
              console.error('🚀 ~ Video generation sync error:', error)
            }
          })
        }

        return response
      } catch (error: any) {
        console.log('🚀 ~ textToVideo error:', error)

        const { $i18n } = useNuxtApp()
        const t = $i18n.t
        // toast.add({
        //   id: 'error',
        //   title: t('Error') || 'Error',
        //   description: error.response?.data?.detail || error.message,
        //   color: 'error'
        // })

        this.errors.textToVideo = error.response?.data?.detail?.error_code || error.message
        return null
      } finally {
        appStore.loading = false
        this.loadings.textToVideo = false
      }
    },

    addImage(image: any) {
      // Check if image already exists in selectedImages
      const exists = this.selectedImages.some(img => img.src === image.src)
      if (!exists) {
        this.selectedImages.push(image)
      }
    },

    removeImage(imageSrc: string) {
      const index = this.selectedImages.findIndex(
        img => img.src === imageSrc
      )
      if (index !== -1) {
        this.selectedImages.splice(index, 1)
      }
    }
  }
})
