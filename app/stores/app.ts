import { defineStore } from 'pinia'

export const useAppStore = defineStore('appStore', {
  state: () => ({
    locale: 'en',
    loading: false,
    isNotificationsSlideoverOpen: false,

    adsBanner: {
      id: '1',
      icon: 'hugeicons:promotion',
      title: 'Enjoy 80% off select Gemini API models. Offer valid until further notice.'
    },

    lastMenus: {
      app: 'app'
    }
  }),
  persist: [
    {
      pick: ['locale', 'lastMenus'],
      storage: localStorage
    }
  ],
  getters: {
    localeForI18n: (state: any) => {
      return state.locale.replace('-', '_').toLowerCase()
    },
    hasHeaderBanner(): boolean {
      const authStore = useAuthStore()

      return !!authStore.isNotVerifyAccount
    }
  }
})
