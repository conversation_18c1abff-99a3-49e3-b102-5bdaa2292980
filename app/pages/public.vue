<script setup lang="ts">
definePageMeta({
  middleware: 'auth'
})
</script>

<template>
  <UPage>
    <UContainer class="max-w-screen-xl px-0">
      <UDashboardPanel
        id="settings"
        :ui="{ body: 'lg:py-12 sm:p-0' }"
      >
        <template #body>
          <div
            class="flex flex-col gap-4 px-4 sm:gap-6 lg:gap-12 w-full mx-auto"
          >
            <NuxtPage />
          </div>
        </template>
      </UDashboardPanel>
    </UContainer>
  </UPage>
</template>
