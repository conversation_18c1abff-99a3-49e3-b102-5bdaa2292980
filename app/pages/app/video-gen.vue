<script setup lang="ts">
import { formatNumber } from '~/utils'
import {
  compareImageArrays,
  commonValidationRules
} from '~/utils/generationValidation'

// SEO meta tags for AI video generator page
useSeoMeta({
  title: 'AI Video Generator - Create Videos from Text with GeminiGen AI',
  description:
    'Generate high-quality AI videos from text prompts using advanced Veo models. Create professional videos, animations, and visual content with AI.',
  ogTitle: 'AI Video Generator - GeminiGen AI',
  ogDescription:
    'Transform text into stunning videos with our AI-powered video generation tool. Multiple models and customization options available.',
  keywords:
    'AI video generator, text to video, AI video creation, artificial intelligence videos, Veo video generation, video AI'
})

// Add structured data for video generator page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebApplication',
        'name': 'GeminiGen AI Video Generator',
        'description':
          'AI-powered video generation tool using advanced Veo models',
        'applicationCategory': 'MultimediaApplication',
        'operatingSystem': 'Web Browser',
        'featureList': [
          'Text-to-Video Generation',
          'Multiple AI Models (Veo 2, Veo 3)',
          'Custom Video Duration',
          'Various Aspect Ratios',
          'High-Quality Video Output',
          'Image-to-Video Conversion'
        ],
        'offers': {
          '@type': 'Offer',
          'description': 'Premium video generation service',
          'priceCurrency': 'USD'
        }
      })
    }
  ]
})

interface ImageFile {
  src: string
  alt: string
  file: File
}
const { authorize } = useAuthorize()
const authStore = useAuthStore()
const user_credit = computed(() => authStore.$state.user?.user_credit)
const isAuthenticated = computed(
  () => !!authStore.$state.access_token && !!authStore.$state.user
)
const { model, models } = useVideoGenModels()
const {
  duration,
  durationOptions, // eslint-disable-line @typescript-eslint/no-unused-vars
  isDurationSelectable, // eslint-disable-line @typescript-eslint/no-unused-vars
  enhancePrompt,
  isEnhancePromptLocked
} = useVideoGenOptions()
const { videoDimension } = useVideoDimensions()
const { resolution, isResolutionSelectable } = useVideoResolution()
const { isServerSelectionEnabled, serviceMode }
  = useServerSelection()
const videoStyle = ref('Cinematic')
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()
const runtimeConfig = useRuntimeConfig() // eslint-disable-line @typescript-eslint/no-unused-vars
const textToVideoStore = useTextToVideoStore()
const {
  textToVideoResult,
  aiToolVideoCardRef,
  prompt,
  negativePrompt,
  loadings,
  errors
} = storeToRefs(textToVideoStore)

// Override default values for hidden fields
duration.value = 8 // Force 8 seconds
enhancePrompt.value = true // Force enhance prompt on
// Removed productStore imports as we now use fixed pricing

// Calculate actual cost based on model, resolution, and server type
const actualCost = computed(() => {
  const isVipServer = serviceMode.value === 'stable'

  // Base pricing for different models and server types
  const getBasePrice = (modelValue: string) => {
    switch (modelValue) {
      case 'veo-2':
        return isVipServer ? 800 : 20 // VIP: 800 credits (original), Regular: 20 credits (discounted)
      case 'veo-3-fast':
        return isVipServer ? 1200 : 100 // VIP: 1200 credits (original), Regular: 100 credits (discounted)
      case 'veo-3':
        return isVipServer ? 640 : 20 // VIP: 640 credits (original), Regular: 20 credits (discounted)
      default:
        return isVipServer ? 800 : 20 // Default to veo-2 pricing
    }
  }

  const basePrice = getBasePrice(model.value?.value || 'veo-2')

  // Full HD pricing adjustments
  if (resolution.value === '1080p') {
    switch (model.value?.value) {
      case 'veo-3-fast':
        return isVipServer ? 640 : 70 // VIP: 640 credits (original), Regular: 70 credits (discounted)
      case 'veo-3':
        return isVipServer ? 1200 : 200 // VIP: 1200 credits (original), Regular: 200 credits (discounted)
      default:
        return basePrice
    }
  }

  return basePrice
})
// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  negativePrompt: '',
  model: models[0],
  videoDimension: '16:9',
  videoStyle: 'Cinematic',
  enhancePrompt: false,
  resolution: models[0]?.resolutions?.default || '720p',
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    negativePrompt: negativePrompt.value,
    model: model.value,
    videoDimension: videoDimension.value,
    videoStyle: videoStyle.value,
    enhancePrompt: enhancePrompt.value,
    resolution: resolution.value,
    selectedImages: [...selectedImages.value]
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = prompt.value !== initialValues.value.prompt
      || negativePrompt.value !== initialValues.value.negativePrompt
      || model.value?.value !== initialValues.value.model?.value
      || videoDimension.value !== initialValues.value.videoDimension
      || videoStyle.value !== initialValues.value.videoStyle
      || enhancePrompt.value !== initialValues.value.enhancePrompt
      || resolution.value !== initialValues.value.resolution

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(
    selectedImages.value,
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToVideoStore.selectedImages = images
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  // Extract File objects from selected images
  const files = selectedImages.value.map(img => img.file).filter(Boolean)

  const result = await textToVideoStore.textToVideo({
    prompt: prompt.value,
    negative_prompt: negativePrompt.value,
    model: model.value?.value || 'veo-2',
    aspect_ratio: videoDimension.value || '16:9',
    enhance_prompt: enhancePrompt.value,
    duration: duration.value,
    resolution: isResolutionSelectable.value ? resolution.value : undefined,
    service_mode: isServerSelectionEnabled.value
      ? serviceMode.value
      : 'unstable',
    files: files
  })

  if (result) {
    const generationType
      = selectedImages.value.length > 0
        ? t('videoGen.imageToVideo')
        : t('videoGen.textToVideo')
    const generationMessage
      = selectedImages.value.length > 0
        ? t('videoGen.imageToVideoGenerationStarted')
        : t('videoGen.textToVideoGenerationStarted')

    toast.add({
      id: 'success',
      title: `${generationType} ${t('videoGen.generated')}`,
      description: generationMessage,
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      negativePrompt: negativePrompt.value,
      model: model.value,
      videoDimension: videoDimension.value,
      videoStyle: videoStyle.value,
      enhancePrompt: enhancePrompt.value,
      resolution: resolution.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('videoGen.pleaseEnterPrompt')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'video',
    hasChanges,
    hasResult: computed(() => !!textToVideoResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const onUsePrompt = (newPrompt: string) => {
  prompt.value = newPrompt
  // scroll to top and focus on prompt input
  nextTick(() => {
    // scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // try to focus the prompt input after scrolling
    setTimeout(() => {
      // look for the prompt input (UChatPrompt component)
      const promptInput = document.querySelector(
        '[class*="chat-prompt"] textarea, [class*="chat-prompt"] input'
      )
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    }, 500)
  })
}

// Keep old functions for potential future use (currently not used in UI)
const addNegativePromptSuggestion = (suggestion: string) => { // eslint-disable-line @typescript-eslint/no-unused-vars
  if (negativePrompt.value) {
    // If there's already content, add a comma and space before the new suggestion
    negativePrompt.value += ', ' + suggestion
  } else {
    // If empty, just add the suggestion
    negativePrompt.value = suggestion
  }
}

const enhancePromptItems = computed(() => { // eslint-disable-line @typescript-eslint/no-unused-vars
  const items = [
    {
      label: t('On'),
      value: 'true',
      description: isEnhancePromptLocked.value
        ? t('videoGen.enhancePromptOnRequired')
        : t('videoGen.enhancePromptOn'),
      disabled: isEnhancePromptLocked.value && !enhancePrompt.value
    },
    {
      label: t('Off'),
      value: 'false',
      description: t('videoGen.enhancePromptOff'),
      disabled: isEnhancePromptLocked.value && enhancePrompt.value
    }
  ]

  return items
})

// Convert enhancePrompt to string for radio group
const enhancePromptString = computed({ // eslint-disable-line @typescript-eslint/no-unused-vars
  get: () => enhancePrompt.value.toString(),
  set: (value: string) => {
    enhancePrompt.value = value === 'true'
  }
})

// Handle buy credits action
const handleBuyCredits = () => {
  errors.value.textToVideo = null
  navigateTo('/profile/credits')
}

const videoInsufficientCreditsMessage = computed(() => {
  const isVipServer = serviceMode.value === 'stable'

  return t('NOT_ENOUGH_CREDIT_MESSAGE_VIDEO_C', {
    money: 10,
    credit: '1M',
    // veo-3-fast pricing
    veo3FastCredit: formatNumber(isVipServer ? 1200 : 100), // VIP: 1200 credits, Regular: 100 credits
    veo3FastPrice: isVipServer ? 6.00 : 0.50, // VIP: $6.00, Regular: $0.50
    veo3FastFullHDCredit: formatNumber(isVipServer ? 640 : 70), // VIP: 640 credits, Regular: 70 credits
    veo3FastFullHDPrice: isVipServer ? 3.20 : 0.35, // VIP: $3.20, Regular: $0.35
    // veo-3 pricing
    veo3Credit: formatNumber(isVipServer ? 640 : 20), // VIP: 640 credits, Regular: 20 credits
    veo3Price: isVipServer ? 3.20 : 0.10, // VIP: $3.20, Regular: $0.10
    veo3FullHDCredit: formatNumber(isVipServer ? 1200 : 200), // VIP: 1200 credits, Regular: 200 credits
    veo3FullHDPrice: isVipServer ? 6.00 : 1.00, // VIP: $6.00, Regular: $1.00
    // veo-2 pricing
    veo2Credit: formatNumber(isVipServer ? 800 : 20), // VIP: 800 credits, Regular: 20 credits
    veo2Price: isVipServer ? 4.00 : 0.10, // VIP: $4.00, Regular: $0.10
    save: 0 // No additional discount
  })
})
</script>

<template>
  <UContainer class="mt-0">
    <!-- SEO H1 Heading -->
    <div class="text-center mb-8">
      <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
        {{ $t("AI Video Generator") }}
      </h1>
      <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        {{
          $t(
            "Create professional videos from text prompts using advanced AI models"
          )
        }}
      </p>
    </div>

    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <div
              v-if="model?.options?.includes('yourImage')"
              class="flex flex-row gap-3 items-end"
            >
              <UFormField :label="$t('videoGen.imageReference')">
                <BaseImageSelect
                  v-model="selectedImages"
                  @update:model-value="handleImagesSelected"
                />
              </UFormField>
              <BaseImageSelectedList
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </div>
          </div>

          <UFormField :label="$t('Prompt')">
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="$t('Describe the video you want to generate...')"
              :rows="6"
            />
          </UFormField>

          <!-- HIDDEN: Negative Prompt Section -->
          <!--
          <div>
            <UFormField :label="$t('videoGen.negativePrompt')">
              <template #hint>
                <UTooltip
                  :delay-duration="0"
                  :text="$t('videoGen.negativePromptTooltip')"
                >
                  <UIcon name="material-symbols:help" />
                </UTooltip>
              </template>
              <UTextarea
                v-model="negativePrompt"
                class="w-full"
                :placeholder="$t('videoGen.negativePromptPlaceholder')"
                :rows="3"
              />
              <template #description>
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  {{ $t("videoGen.negativePromptDescription") }}
                </span>
                <div class="space-y-2 mt-0">
                  <div
                    class="text-xs font-medium text-gray-600 dark:text-gray-300"
                  >
                    {{ $t("videoGen.negativePromptSuggestions") }}:
                  </div>
                  <div class="flex flex-wrap gap-2">
                    <UButton
                      v-for="i in 6"
                      :key="i"
                      size="xs"
                      variant="outline"
                      color="neutral"
                      :label="$t(`videoGen.negativePromptSuggestion${i}`)"
                      @click="
                        addNegativePromptSuggestion(
                          $t(`videoGen.negativePromptSuggestion${i}`)
                        )
                      "
                    />
                  </div>
                </div>
              </template>
            </UFormField>
          </div>
          -->
          <div class="space-y-6">
            <!-- HIDDEN: First row: Enhance Prompt and Aspect Ratio -->
            <!--
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('videoGen.enhancePrompt')">
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="
                      isEnhancePromptLocked
                        ? $t('videoGen.enhancePromptLocked')
                        : enhancePrompt
                          ? $t('videoGen.enhancePromptOn')
                          : $t('videoGen.enhancePromptNotRefined')
                    "
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <URadioGroup
                  v-model="enhancePromptString"
                  orientation="horizontal"
                  variant="card"
                  value-key="value"
                  :items="enhancePromptItems"
                  size="xs"
                  :disabled="isEnhancePromptLocked"
                />
              </UFormField>

              <UFormField :label="$t('videoGen.aspectRatio')">
                <BaseVideoDimensionsSelect :options="model?.ratios" />
              </UFormField>
            </div>
            -->

            <!-- Aspect Ratio and Resolution -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <UFormField :label="$t('videoGen.aspectRatio')">
                <BaseVideoDimensionsSelect :options="model?.ratios" />
              </UFormField>

              <UFormField
                v-if="isResolutionSelectable"
                :label="$t('videoGen.resolution')"
              >
                <template #hint>
                  <UTooltip
                    :delay-duration="0"
                    :text="$t('videoGen.selectResolution')"
                  >
                    <UIcon name="material-symbols:help" />
                  </UTooltip>
                </template>
                <BaseVideoResolutionSelect class="" />
              </UFormField>
            </div>

            <!-- Server Selection -->
            <UFormField
              v-if="isServerSelectionEnabled"
              :label="$t('Server')"
            >
              <BaseServerSelect />
            </UFormField>


          </div>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div
              class="text-xs text-right space-y-1"
            >
              <div>
                {{
                  $t("videoGen.creditsRemaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary font-medium">
                {{
                  $t("videoGen.generationCostPerVideo", {
                    cost: formatNumber(actualCost)
                  })
                }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ $t("videoGen.fixedPricePerVideo") }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('videoGen.generateVideo')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 !cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToVideo']"
              :disabled="!prompt"
              @click="authorize(onGenerate)"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="
          (textToVideoResult || loadings['textToVideo'])
            && !errors['textToVideo']
        "
        ref="aiToolVideoCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolVideoCard
          v-bind="textToVideoResult"
          :data="textToVideoResult"
          :loading="loadings['textToVideo']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div
          v-if="errors['textToVideo']"
          class="flex flex-col items-center justify-center h-full p-8"
        >
          <!-- Enhanced display for NOT_ENOUGH_AND_LOCK_CREDIT error -->
          <InsufficientCreditsError
            v-if="
              ['NOT_ENOUGH_CREDIT', 'NOT_ENOUGH_AND_LOCK_CREDIT'].includes(
                errors['textToVideo']
              )
            "
            :credits-needed="actualCost"
            :available-credits="user_credit?.available_credit || 0"
            generation-type="video"
            :message="videoInsufficientCreditsMessage"
            @buy-credits="handleBuyCredits"
            @view-pricing="$router.push('/pricing')"
          />

          <!-- Default error display for other errors -->
          <div
            v-else
            class="text-center space-y-4"
          >
            <div>
              <UIcon
                name="i-lucide-alert-circle"
                class="text-6xl mb-2 text-error"
              />
            </div>
            <div class="text-sm text-error">
              {{ $t(errors["textToVideo"] || "videoGen.somethingWentWrong") }}
            </div>
          </div>
        </div>
        <div
          v-else
          class="h-full"
        >
          <div class="mb-4 text-center">
            <h3
              class="text-lg font-semibold text-gray-900 dark:text-white mb-2"
            >
              {{ $t("videoGen.examplesTitle") }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ $t("videoGen.examplesDescription") }}
            </p>
          </div>
          <BaseVideoComparisonDemo :height="'350px'" />
        </div>
      </UCard>
    </div>
    <!-- Video Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <VideoPromptGallery
        class="mt-8"
        @use-prompt="onUsePrompt"
      />
    </Motion>

    <!-- Buy Credits Drawer -->
    <BuyCreditsDrawer />
  </UContainer>
</template>
