<script setup lang="ts">
const { t } = useI18n()

// Enhanced SEO meta tags for terms page
useSeoMeta({
  title: 'Terms of Service - GeminiGen AI Legal Terms',
  description: 'Read the terms of service for GeminiGen AI platform. Understand your rights and responsibilities when using our AI content generation services.',
  ogTitle: 'Terms of Service - GeminiGen AI',
  ogDescription: 'Legal terms and conditions for using GeminiGen AI platform and services.',
  keywords: 'terms of service, legal terms, GeminiGen AI terms, user agreement, service conditions'
})

// Add structured data for terms page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        'name': 'Terms of Service',
        'description': 'Legal terms and conditions for GeminiGen AI services',
        'mainEntity': {
          '@type': 'TermsOfService',
          'name': 'GeminiGen AI Terms of Service',
          'description': 'Legal agreement between users and GeminiGen AI'
        }
      })
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <!-- SEO H1 Heading -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {{ $t('terms.title') }}
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          {{ $t('terms.description') }}
        </p>
      </div>

      <UPageHeader
        :title="$t('terms.title')"
        :description="$t('terms.description')"
        class="sr-only"
      />
    </UContainer>

    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>{{ $t("terms.lastUpdated") }}</strong> {{ $t("terms.lastUpdatedDate") }}</p>
        </div>

        <div class="mb-8">
          <p class="text-lg">
            {{ $t("terms.introduction") }}
          </p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.acceptanceOfTerms") }}</h2>
          <p>{{ $t("terms.acceptanceOfTermsDescription") }}</p>
          <p>{{ $t("terms.acceptanceOfTermsDetails") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.userAccounts") }}</h2>
          <p>{{ $t("terms.userAccountsDescription") }}</p>
          <ul>
            <li>{{ $t("terms.userAccounts1") }}</li>
            <li>{{ $t("terms.userAccounts2") }}</li>
            <li>{{ $t("terms.userAccounts3") }}</li>
            <li>{{ $t("terms.userAccounts4") }}</li>
            <li>{{ $t("terms.userAccounts5") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.paymentAndBilling") }}</h2>
          <p>{{ $t("terms.paymentAndBillingDescription") }}</p>
          <ul>
            <li>{{ $t("terms.payment1") }}</li>
            <li>{{ $t("terms.payment2") }}</li>
            <li>{{ $t("terms.payment3") }}</li>
            <li>{{ $t("terms.payment4") }}</li>
            <li>{{ $t("terms.payment5") }}</li>
            <li>{{ $t("terms.payment6") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.useOfService") }}</h2>
          <p>{{ $t("terms.serviceUsageDescription") }}</p>
          <h3>{{ $t("terms.permittedUse") }}</h3>
          <ul>
            <li>{{ $t("terms.permitted1") }}</li>
            <li>{{ $t("terms.permitted2") }}</li>
            <li>{{ $t("terms.permitted3") }}</li>
            <li>{{ $t("terms.permitted4") }}</li>
          </ul>

          <h3>{{ $t("terms.prohibitedUse") }}</h3>
          <ul>
            <li>{{ $t("terms.prohibited1") }}</li>
            <li>{{ $t("terms.prohibited2") }}</li>
            <li>{{ $t("terms.prohibited3") }}</li>
            <li>{{ $t("terms.prohibited4") }}</li>
            <li>{{ $t("terms.prohibited5") }}</li>
            <li>{{ $t("terms.prohibited6") }}</li>
            <li>{{ $t("terms.prohibited7") }}</li>
            <li>{{ $t("terms.prohibited8") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.intellectualProperty") }}</h2>
          <p>{{ $t("terms.intellectualPropertyDescription") }}</p>
          <h3>{{ $t("terms.ourIntellectualProperty") }}</h3>
          <p>{{ $t("terms.ourIntellectualPropertyDescription") }}</p>

          <h3>{{ $t("terms.userGeneratedContent") }}</h3>
          <p>{{ $t("terms.userGeneratedContentDescription") }}</p>
          <ul>
            <li>{{ $t("terms.userContent1") }}</li>
            <li>{{ $t("terms.userContent2") }}</li>
            <li>{{ $t("terms.userContent3") }}</li>
            <li>{{ $t("terms.userContent4") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.limitationOfLiability") }}</h2>
          <p>{{ $t("terms.limitationOfLiabilityDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.disclaimers") }}</h2>
          <p>{{ $t("terms.disclaimersDescription") }}</p>
          <ul>
            <li>{{ $t("terms.disclaimer1") }}</li>
            <li>{{ $t("terms.disclaimer2") }}</li>
            <li>{{ $t("terms.disclaimer3") }}</li>
            <li>{{ $t("terms.disclaimer4") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.termination") }}</h2>
          <p>{{ $t("terms.terminationDescription") }}</p>
          <h3>{{ $t("terms.terminationByUser") }}</h3>
          <p>{{ $t("terms.terminationByUserDescription") }}</p>

          <h3>{{ $t("terms.terminationByUs") }}</h3>
          <p>{{ $t("terms.terminationByUsDescription") }}</p>
          <ul>
            <li>{{ $t("terms.termination1") }}</li>
            <li>{{ $t("terms.termination2") }}</li>
            <li>{{ $t("terms.termination3") }}</li>
            <li>{{ $t("terms.termination4") }}</li>
            <li>{{ $t("terms.termination5") }}</li>
            <li>{{ $t("terms.termination6") }}</li>
          </ul>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.governingLaw") }}</h2>
          <p>{{ $t("terms.governingLawDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("terms.contactUsTerms") }}</h2>
          <p>{{ $t("terms.contactUsTermsDescription") }}</p>
        </div>
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p><strong>{{ $t("terms.contactEmail") }}</strong> <EMAIL></p>
          <p><strong>{{ $t("terms.contactAddress") }}</strong></p>
          <p>{{ $t("terms.companyAddress") }}</p>
        </div>
      </div>
    </UContainer>
  </UPage>
</template>
