<script setup lang="ts">
const { t } = useI18n()

// Enhanced SEO meta tags for privacy page
useSeoMeta({
  title: 'Privacy Policy - GeminiGen AI Data Protection',
  description: 'Learn how GeminiGen AI protects your privacy and handles your data. Read our comprehensive privacy policy for AI content generation services.',
  ogTitle: 'Privacy Policy - GeminiGen AI',
  ogDescription: 'Understand how we protect your privacy and secure your data when using GeminiGen AI services.',
  keywords: 'privacy policy, data protection, GeminiGen AI privacy, user data security, privacy rights'
})

// Add structured data for privacy page
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        'name': 'Privacy Policy',
        'description': 'Privacy policy and data protection information for GeminiGen AI',
        'mainEntity': {
          '@type': 'PrivacyPolicy',
          'name': 'GeminiGen AI Privacy Policy',
          'description': 'Data protection and privacy practices of GeminiGen AI'
        }
      })
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <!-- SEO H1 Heading -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
          {{ $t('privacy.title') }}
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          {{ $t('privacy.description') }}
        </p>
      </div>

      <UPageHeader
        :title="$t('privacy.title')"
        :description="$t('privacy.description')"
        class="sr-only"
      />
    </UContainer>
    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <div class="mb-6 text-sm text-gray-600 dark:text-gray-400">
          <p>
            <strong>{{ $t("privacy.lastUpdated") }}</strong>
            {{ $t("privacy.lastUpdatedDate") }}
          </p>
        </div>

        <div class="mb-8">
          <p class="text-lg">
            {{ $t("privacy.introduction") }}
          </p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.informationWeCollect") }}</h2>
          <p>{{ $t("privacy.informationCollectionDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.creditCalculation") }}</h2>
          <p>{{ $t("privacy.creditCalculationDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.paymentSecurity") }}</h2>
          <p>{{ $t("privacy.paymentSecurityDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.emailNotification") }}</h2>
          <p>{{ $t("privacy.emailNotificationDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.dataSecurity") }}</h2>
          <p>{{ $t("privacy.dataSecurityDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.thirdPartyServices") }}</h2>
          <p>{{ $t("privacy.thirdPartyServicesDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.cookies") }}</h2>
          <p>{{ $t("privacy.cookiesDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.thirdPartyLinks") }}</h2>
          <p>{{ $t("privacy.thirdPartyLinksDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.childrenPrivacy") }}</h2>
          <p>{{ $t("privacy.childrenPrivacyDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.policyChanges") }}</h2>
          <p>{{ $t("privacy.policyChangesDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.commercialUse") }}</h2>
          <p>{{ $t("privacy.commercialUseDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.otherPeoplePrivacy") }}</h2>
          <p>{{ $t("privacy.otherPeoplePrivacyDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.unsubscribe") }}</h2>
          <p>{{ $t("privacy.unsubscribeDescription") }}</p>
        </div>

        <div class="mb-8">
          <h2>{{ $t("privacy.contactUs") }}</h2>
        </div>
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p><strong>{{ $t("terms.contactEmail") }}</strong> <EMAIL></p>
          <p><strong>{{ $t("terms.contactAddress") }}</strong></p>
          <p>{{ $t("terms.companyAddress") }}</p>
        </div>
      </div>
    </UContainer>
  </UPage>
</template>
