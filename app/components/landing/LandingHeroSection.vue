<template>
  <section
    class="relative h-screen flex items-center justify-center overflow-hidden"
  >
    <!-- Video Background -->
    <div class="absolute inset-0 z-0">
      <video
        ref="heroVideo"
        autoplay
        muted
        loop
        playsinline
        preload="auto"
        disablepictureinpicture
        controlslist="nodownload"
        class="w-full h-full object-cover"
      >
        <source
          src="/videos/landing-hero.mp4"
          type="video/mp4"
          autoplay
        >
      </video>
      <!-- Dark overlay for better text readability -->
      <div class="absolute inset-0 bg-black/50" />
    </div>

    <!-- Hero Content -->
    <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-6">
      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.2
        }"
      >
        <h1
          class="text-4xl md:text-6xl lg:text-6xl font-bold mb-6 leading-tight"
        >
          {{ t("hero.title") }}
        </h1>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.4
        }"
      >
        <div class="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
          <p class="mb-4">
            {{
              $t(
                "Transform your ideas into stunning AI-generated images, videos, speech, and more."
              )
            }}
          </p>
          <p class="flex flex-wrap items-center justify-center gap-2">
            <i18n-t
              keypath="Save up to {0} compared to traditional creative services while experiencing the future of content generation."
            >
              <a
                href="/privacy"
                target="_blank"
                class="text-primary-500"
              >
                <Motion
                  :initial="{ scale: 0.8, opacity: 0, rotateY: -90 }"
                  :animate="{ scale: 1, opacity: 1, rotateY: 0 }"
                  :transition="{
                    duration: 1.2,
                    delay: 1.0,
                    type: 'spring',
                    stiffness: 100
                  }"
                  class="inline-block"
                >
                  <span class="relative inline-block savings-highlight">
                    <span
                      class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse"
                      style="text-shadow: 0 0 20px rgba(255, 165, 0, 0.5)"
                    >
                      80%
                    </span>
                    <!-- Sparkle effects -->
                    <Motion
                      :initial="{ scale: 0, opacity: 0 }"
                      :animate="{ scale: 1, opacity: 1 }"
                      :transition="{
                        duration: 0.8,
                        delay: 1.5,
                        repeat: Infinity,
                        repeatType: 'reverse',
                        repeatDelay: 2
                      }"
                      class="absolute -top-2 -right-2"
                    >
                      <div
                        class="w-3 h-3 bg-yellow-400 rounded-full animate-ping"
                      />
                    </Motion>
                    <Motion
                      :initial="{ scale: 0, opacity: 0 }"
                      :animate="{ scale: 1, opacity: 1 }"
                      :transition="{
                        duration: 0.8,
                        delay: 2.0,
                        repeat: Infinity,
                        repeatType: 'reverse',
                        repeatDelay: 1.5
                      }"
                      class="absolute -top-1 -left-3"
                    >
                      <UIcon
                        name="lucide:sparkles"
                        class="w-4 h-4 text-yellow-300 sparkle"
                      />
                    </Motion>
                    <Motion
                      :initial="{ scale: 0, opacity: 0 }"
                      :animate="{ scale: 1, opacity: 1 }"
                      :transition="{
                        duration: 0.8,
                        delay: 2.5,
                        repeat: Infinity,
                        repeatType: 'reverse',
                        repeatDelay: 1.8
                      }"
                      class="absolute -bottom-1 -right-4"
                    >
                      <UIcon
                        name="lucide:star"
                        class="w-3 h-3 text-orange-400 sparkle"
                      />
                    </Motion>
                  </span>
                </Motion>
              </a>
            </i18n-t>
          </p>
        </div>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.6
        }"
      >
        <div
          class="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <UButton
            size="xl"
            color="primary"
            variant="solid"
            :label="t('hero.cta.primary')"
            to="/app"
            icon="mingcute:ai-fill"
          />
        </div>
      </Motion>

      <Motion
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.8,
          delay: 0.8
        }"
      >
        <div class="mt-12 text-sm text-gray-300">
          <p>{{ t("hero.users") }}</p>
        </div>
      </Motion>
    </div>

    <!-- Scroll indicator -->
    <Motion
      :initial="{
        opacity: 0,
        y: -20
      }"
      :animate="{
        opacity: 1,
        y: 0
      }"
      :transition="{
        duration: 1,
        delay: 1.2,
        repeat: Infinity,
        repeatType: 'reverse'
      }"
      class="absolute bottom-8 left-1/2 transform -translate-x-1/2"
    >
      <div class="flex flex-col items-center text-white/70">
        <span class="text-sm mb-2">{{ t("hero.scroll") }}</span>
        <UIcon
          name="lucide:chevron-down"
          class="w-6 h-6"
        />
      </div>
    </Motion>
  </section>
</template>

<script lang="ts" setup>
const heroVideo = ref<HTMLVideoElement | null>(null)

onMounted(() => {
  if (heroVideo.value) {
    heroVideo.value.muted = true
    heroVideo.value.loop = true

    // play after load
    heroVideo.value.addEventListener('loadeddata', () => {
      heroVideo.value?.play().catch(() => {
        // Ignore autoplay errors
      })
    })
  }
})

const { t } = useI18n()
</script>

<style scoped>
/* Ensure video covers the entire section */
video {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

/* Glowing effect for the 60% text */
.savings-highlight {
  position: relative;
  display: inline-block;
}

.savings-highlight::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
  border-radius: 10px;
  opacity: 0.3;
  filter: blur(10px);
  animation: glow-rotate 3s linear infinite;
  z-index: -1;
}

@keyframes glow-rotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* Sparkle animation */
@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

.sparkle {
  animation: sparkle 2s ease-in-out infinite;
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }

  p {
    font-size: 1.125rem;
  }

  .savings-highlight span {
    font-size: 2.5rem !important;
  }
}
</style>
