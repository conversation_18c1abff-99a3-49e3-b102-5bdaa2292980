<script setup lang="ts">
const { t } = useI18n()

// FAQ questions with i18n
const allQuestions = computed(() => [
  {
    title: t('faq.questions.q1.title'),
    content: t('faq.questions.q1.content')
  },
  {
    title: t('faq.questions.q2.title'),
    content: t('faq.questions.q2.content')
  },
  {
    title: t('faq.questions.q3.title'),
    content: t('faq.questions.q3.content')
  },
  {
    title: t('faq.questions.q4.title'),
    content: t('faq.questions.q4.content')
  },
  {
    title: t('faq.questions.q5.title'),
    content: t('faq.questions.q5.content')
  },
  {
    title: t('faq.questions.q6.title'),
    content: t('faq.questions.q6.content')
  },
  {
    title: t('faq.questions.q7.title'),
    content: t('faq.questions.q7.content')
  },
  {
    title: t('faq.questions.q8.title'),
    content: t('faq.questions.q8.content')
  },
  {
    title: t('faq.questions.q9.title'),
    content: t('faq.questions.q9.content')
  },
  {
    title: t('faq.questions.q10.title'),
    content: t('faq.questions.q10.content')
  },
  {
    title: t('faq.questions.q11.title'),
    content: t('faq.questions.q11.content')
  },
  {
    title: t('faq.questions.q12.title'),
    content: t('faq.questions.q12.content')
  },
  {
    title: t('faq.questions.q13.title'),
    content: t('faq.questions.q13.content')
  },
  {
    title: t('faq.questions.q14.title'),
    content: t('faq.questions.q14.content')
  },
  {
    title: t('faq.questions.q15.title'),
    content: t('faq.questions.q15.content')
  },
  {
    title: t('faq.questions.q16.title'),
    content: t('faq.questions.q16.content')
  },
  {
    title: t('faq.questions.q17.title'),
    content: t('faq.questions.q17.content')
  },
  {
    title: t('faq.questions.q18.title'),
    content: t('faq.questions.q18.content')
  },
  {
    title: t('faq.questions.q19.title'),
    content: t('faq.questions.q19.content')
  }
])

// Group questions into categories for better organization
const faqData = computed(() => {
  const questionsPerCategory = Math.ceil(allQuestions.value.length / 3)

  return {
    title: t('faq.title'),
    description: t('faq.description'),
    categories: [
      {
        title: t('faq.general.title'),
        questions: allQuestions.value.slice(0, questionsPerCategory)
      },
      {
        title: t('faq.services.title'),
        questions: allQuestions.value.slice(questionsPerCategory, questionsPerCategory * 2)
      },
      {
        title: t('faq.pricing.title'),
        questions: allQuestions.value.slice(questionsPerCategory * 2)
      }
    ].filter(category => category.questions.length > 0)
  }
})

const selectedCategory = ref(0)
</script>

<template>
  <section class="py-24 bg-gradient-to-b from-muted/30 to-background">
    <UContainer class="max-w-6xl">
      <Motion
        :initial="{
          opacity: 0,
          y: 50
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8
        }"
        class="text-center mb-16"
      >
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
          {{ faqData.title }}
        </h2>
        <p class="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto">
          {{ faqData.description }}
        </p>
      </Motion>

      <!-- Category tabs -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.6,
          delay: 0.2
        }"
      >
        <div class="flex flex-wrap justify-center gap-4 mb-12">
          <button
            v-for="(category, index) in faqData.categories"
            :key="index"
            class="px-6 py-3 rounded-full font-medium transition-all duration-300"
            :class="selectedCategory === index
              ? 'bg-primary text-primary-foreground shadow-lg'
              : 'bg-card text-muted-foreground hover:bg-primary/10 hover:text-primary border border-border'"
            @click="selectedCategory = index"
          >
            {{ category.title }}
          </button>
        </div>
      </Motion>

      <!-- FAQ Content -->
      <Motion
        :key="selectedCategory"
        :initial="{
          opacity: 0,
          x: 20
        }"
        :animate="{
          opacity: 1,
          x: 0
        }"
        :transition="{
          duration: 0.5
        }"
      >
        <div class="space-y-4">
          <Motion
            v-for="(question, index) in faqData.categories[selectedCategory].questions"
            :key="index"
            :initial="{
              opacity: 0,
              y: 20
            }"
            :animate="{
              opacity: 1,
              y: 0
            }"
            :transition="{
              duration: 0.4,
              delay: index * 0.1
            }"
          >
            <UAccordion
              :items="[{
                label: question.title,
                content: question.content,
                defaultOpen: index === 0
              }]"
              :ui="{
                wrapper: 'space-y-0',
                item: {
                  base: 'border border-border rounded-lg overflow-hidden',
                  padding: 'p-0'
                },
                trigger: {
                  base: 'flex items-center gap-3 w-full text-left p-6 hover:bg-muted/50 transition-colors duration-200',
                  label: 'text-lg font-semibold'
                },
                content: {
                  base: 'text-muted-foreground px-6 pb-6 pt-0 leading-relaxed'
                },
                trailingIcon: {
                  base: 'w-5 h-5 text-muted-foreground transition-transform duration-200'
                }
              }"
              class="mb-4"
            />
          </Motion>
        </div>
      </Motion>

      <!-- Contact support -->
      <Motion
        :initial="{
          opacity: 0,
          y: 30
        }"
        :animate="{
          opacity: 1,
          y: 0
        }"
        :transition="{
          duration: 0.8,
          delay: 0.6
        }"
        class="text-center mt-16"
      >
        <div class="p-8 rounded-2xl bg-gradient-to-r from-primary/10 to-violet-500/10 border border-primary/20">
          <h3 class="text-xl font-bold mb-4">
            {{ t('faq.contact.title') }}
          </h3>
          <p class="text-muted-foreground mb-6">
            {{ t('faq.contact.description') }}
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <UButton
              size="lg"
              color="primary"
              variant="solid"
              :label="t('faq.contact.email')"
              icon="lucide:mail"
              to="mailto:<EMAIL>"
              class="px-6"
            />
            <UButton
              size="lg"
              color="primary"
              variant="outline"
              :label="t('faq.contact.discord')"
              icon="i-simple-icons-discord"
              to="https://discord.gg/vJnYe86T8F"
              target="_blank"
              class="px-6"
            />
          </div>
        </div>
      </Motion>
    </UContainer>
  </section>
</template>

<style scoped>
/* Gradient text effect */
h2 {
  background: linear-gradient(135deg, rgb(var(--color-primary-500)), rgb(var(--color-violet-500)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Tab hover effects */
button:hover {
  transform: translateY(-2px);
}

/* Accordion custom styling */
.UAccordion {
  background: rgb(var(--color-card));
}
</style>
