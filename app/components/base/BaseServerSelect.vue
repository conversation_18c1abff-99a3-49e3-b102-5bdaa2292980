<script setup lang="ts">
const { selectedServer, serverOptions } = useServerSelection()
const { t } = useI18n()
</script>

<template>
  <URadioGroup
    v-model="selectedServer"
    orientation="horizontal"
    variant="card"
    value-key="value"
    :items="serverOptions"
    :ui="{
      fieldset: 'space-x-4',
      item: 'h-full p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors cursor-pointer'
    }"
  >
    <template #label="{ item }">
      <div class="flex items-start justify-between w-full h-full">
        <div class="flex items-start gap-3">
          <div class="flex-1">
            <div class="flex items-center gap-2">
              <UIcon
                :name="item.icon"
                class="w-4 h-4 text-primary-500"
              />
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {{ $t(item.label) }}
              </span>
              <UBadge
                :color="item.badgeColor"
                variant="soft"
                size="xs"
              >
                {{ $t(item.badge) }}
              </UBadge>
            </div>
            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
              {{ $t(item.description_c) }}
            </p>
          </div>
        </div>
      </div>
    </template>
  </URadioGroup>
</template>
