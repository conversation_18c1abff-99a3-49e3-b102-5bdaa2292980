<script setup lang="ts">
interface VideoComparisonExample {
  id: string
  beforeContent: string
  afterContent: string
  beforeLabel: string
  afterLabel: string
  title: string
  description?: string
  type: 'text-to-video' | 'image-to-video'
  beforeType: 'text' | 'image'
  afterType: 'video'
}

interface Props {
  examples?: VideoComparisonExample[]
  height?: string
  autoplay?: boolean
  interval?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  autoplay: true,
  interval: 8000,
  examples: () => [
    {
      id: '1',
      beforeContent: 'A majestic eagle soaring through mountain peaks at sunset, golden hour lighting, cinematic shot',
      afterContent: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
      beforeLabel: 'Text Prompt',
      afterLabel: 'Generated Video',
      title: 'Text to Video Generation',
      description: 'Transform text descriptions into stunning videos with AI',
      type: 'text-to-video',
      beforeType: 'text',
      afterType: 'video'
    },
    {
      id: '2',
      beforeContent: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      afterContent: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4',
      beforeLabel: 'Source Image',
      afterLabel: 'Animated Video',
      title: 'Image to Video Animation',
      description: 'Bring static images to life with AI video generation',
      type: 'image-to-video',
      beforeType: 'image',
      afterType: 'video'
    }
  ]
})

const currentIndex = ref(0)
const isAutoplayActive = ref(props.autoplay)
const isHovered = ref(false)
let autoplayTimer: NodeJS.Timeout | null = null

// Tutorial state
const showTutorial = ref(false)
const isFirstVisit = ref(true)
const tutorialSliderPosition = ref(50)
let tutorialTimer: NodeJS.Timeout | null = null
let sliderDemoTimer: NodeJS.Timeout | null = null

const startAutoplay = () => {
  if (!isAutoplayActive.value || props.examples.length <= 1) return

  autoplayTimer = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.examples.length
  }, props.interval)
}

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

const toggleAutoplay = () => {
  isAutoplayActive.value = !isAutoplayActive.value
  if (isAutoplayActive.value) {
    startAutoplay()
  } else {
    stopAutoplay()
  }
}

const goToSlide = (index: number) => {
  currentIndex.value = index
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % props.examples.length
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

const prevSlide = () => {
  currentIndex.value
    = currentIndex.value === 0
      ? props.examples.length - 1
      : currentIndex.value - 1
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

onMounted(() => {
  // Check if user has seen tutorial before
  if (typeof window !== 'undefined') {
    const hasSeenTutorial = localStorage.getItem('video-comparison-tutorial-seen')
    if (hasSeenTutorial) {
      isFirstVisit.value = false
    }
  }

  if (isAutoplayActive.value) {
    startAutoplay()
  }

  // Start tutorial for first-time users
  if (isFirstVisit.value) {
    startTutorial()
  }
})

onUnmounted(() => {
  stopAutoplay()

  // Clean up tutorial timers
  if (tutorialTimer) {
    clearTimeout(tutorialTimer)
  }
  if (sliderDemoTimer) {
    clearTimeout(sliderDemoTimer)
  }
})

// Pause autoplay on hover
const onMouseEnter = () => {
  isHovered.value = true
  if (isAutoplayActive.value) {
    stopAutoplay()
  }
}

const onMouseLeave = () => {
  isHovered.value = false
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

// Tutorial functions
const startTutorial = () => {
  if (!isFirstVisit.value) return

  // Show tutorial popup after 1 second
  tutorialTimer = setTimeout(() => {
    showTutorial.value = true
    startSliderDemo()
  }, 1000)
}

const startSliderDemo = () => {
  let direction = 1
  const animateSlider = () => {
    if (!showTutorial.value || isHovered.value) return

    if (direction === 1) {
      tutorialSliderPosition.value += 2
      if (tutorialSliderPosition.value >= 80) {
        direction = -1
      }
    } else {
      tutorialSliderPosition.value -= 2
      if (tutorialSliderPosition.value <= 20) {
        direction = 1
      }
    }

    sliderDemoTimer = setTimeout(animateSlider, 50)
  }

  animateSlider()
}

const closeTutorial = () => {
  showTutorial.value = false
  isFirstVisit.value = false

  // Clean up timers
  if (sliderDemoTimer) {
    clearTimeout(sliderDemoTimer)
    sliderDemoTimer = null
  }

  // Save to localStorage
  if (typeof window !== 'undefined') {
    localStorage.setItem('video-comparison-tutorial-seen', 'true')
  }
}
</script>

<template>
  <div
    class="relative w-full"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <!-- Main Comparison Slider -->
    <div class="relative">
      <BaseVideoComparisonSlider
        v-if="props.examples[currentIndex]"
        :before-content="props.examples[currentIndex]?.beforeContent || ''"
        :after-content="props.examples[currentIndex]?.afterContent || ''"
        :before-label="props.examples[currentIndex]?.beforeLabel || 'Before'"
        :after-label="props.examples[currentIndex]?.afterLabel || 'After'"
        :before-type="props.examples[currentIndex]?.beforeType || 'text'"
        :after-type="props.examples[currentIndex]?.afterType || 'video'"
        :height="props.height"
        :initial-position="showTutorial ? tutorialSliderPosition : 50"
        @mouseenter="onMouseEnter"
        @mouseleave="onMouseLeave"
      />

      <!-- Tutorial Overlay -->
      <div
        v-if="showTutorial"
        class="absolute inset-0 bg-black/20 backdrop-blur-sm z-40 flex items-center justify-center"
        @click="closeTutorial"
      >
        <div
          class="bg-white dark:bg-gray-800 rounded-lg p-6 mx-4 max-w-sm shadow-xl border border-gray-200 dark:border-gray-700"
          @click.stop
        >
          <div class="flex items-center mb-4">
            <UIcon
              name="i-lucide-mouse-pointer-click"
              class="w-6 h-6 text-primary-500 mr-3"
            />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ $t('Try the Video Comparison!') }}
            </h3>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {{ $t('Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.') }}
          </p>
          <div class="flex justify-end space-x-2">
            <UButton
              variant="ghost"
              size="sm"
              class="cursor-pointer"
              @click="closeTutorial"
            >
              {{ $t('Got it!') }}
            </UButton>
          </div>
        </div>
      </div>

      <!-- Autoplay Control - Bottom Right -->
      <div
        v-if="props.examples.length > 1"
        class="absolute bottom-4 right-4 z-30"
      >
        <UButton
          class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 backdrop-blur-sm cursor-pointer"
          @click="toggleAutoplay"
        >
          <UIcon
            :name="isAutoplayActive ? 'i-lucide-pause' : 'i-lucide-play'"
            class="w-4 h-4"
          />
        </UButton>
      </div>

      <!-- Navigation Arrows -->
      <UButton
        v-if="props.examples.length > 1"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-20"
        @click="prevSlide"
      >
        <UIcon
          name="i-lucide-chevron-left"
          class="w-5 h-5"
        />
      </UButton>

      <UButton
        v-if="props.examples.length > 1"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-20"
        @click="nextSlide"
      >
        <UIcon
          name="i-lucide-chevron-right"
          class="w-5 h-5"
        />
      </UButton>
    </div>

    <!-- Example Info -->
    <div class="mt-4 text-center">
      <h4 class="text-base font-medium text-gray-900 dark:text-white mb-1">
        {{ props.examples[currentIndex]?.title }}
      </h4>
      <p class="text-sm text-gray-600 dark:text-gray-400">
        {{ props.examples[currentIndex]?.description }}
      </p>
    </div>

    <!-- Dots Indicator -->
    <div
      v-if="props.examples.length > 1"
      class="flex justify-center mt-4 space-x-2"
    >
      <UButton
        v-for="(example, index) in props.examples"
        :key="example.id"
        class="w-2 h-2 rounded-full transition-all duration-200"
        :class="
          index === currentIndex
            ? 'bg-primary-500'
            : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
        "
        @click="goToSlide(index)"
      />
    </div>
  </div>
</template>
