<script setup lang="ts">
interface ModelSelectProps {
  modelValue: any
  models: any[]
}

const props = defineProps<ModelSelectProps>()
const emit = defineEmits<{
  'update:modelValue': [value: any]
}>()
const runtimeConfig = useRuntimeConfig()

const updateModel = (value: any) => {
  // Check if this is Gemini 2.5 Pro and beta is enabled
  if (runtimeConfig.public.features.beta && value?.label === 'Gemini 2.5 Pro') {
    // Don't update if it's disabled
    return
  }
  emit('update:modelValue', value)
}
</script>

<template>
  <USelectMenu
    :model-value="props.modelValue"
    :items="props.models"
    size="sm"
    icon="hugeicons:ai-chip"
    class="min-w-40 hover:bg-default focus:bg-default data-[state=open]:bg-default"
    :ui="{
      trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200',
      content: 'w-56'
    }"
    @update:model-value="updateModel"
  >
    <template #item="{ item: option }">
      <div class="flex items-center justify-between w-full">
        <div
          class="flex items-center gap-2"
          :class="[
            runtimeConfig.public.features.beta && option.label === 'Gemini 2.5 Pro'
              ? 'opacity-50'
              : ''
          ]"
        >
          <UIcon
            :name="option.icon"
            class="w-4 h-4"
          />
          <span>{{ option.label }}</span>
        </div>
        <div
          v-if="runtimeConfig.public.features.beta && option.label === 'Gemini 2.5 Pro'"
          class="bg-orange-500 text-white text-[8px] px-1.5 py-0.5 rounded-sm"
        >
          {{ $t('Coming Soon') }}
        </div>
      </div>
    </template>
  </USelectMenu>
</template>
