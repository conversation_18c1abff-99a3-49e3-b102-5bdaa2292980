<script setup lang="ts">
const { t } = useI18n()
const authStore = useAuthStore()
const { user } = storeToRefs(authStore)
const runtimeConfig = useRuntimeConfig()

const items = computed(() => {
  const profileSection = [
    {
      label: t('userMenu.profile'),
      icon: 'i-lucide-user',
      to: '/profile'
    }
  ]

  // Add buyCredits menu only if beta feature is disabled
  // if (!runtimeConfig.public.features.beta) {
    profileSection.push({
      label: t('userMenu.buyCredits'),
      icon: 'mynaui:cart-plus',
      to: '/profile/credits'
    })
  // }

  return [
    [
      {
        label: user.value?.full_name,
        avatar: {
          icon: 'solar:user-bold-duotone'
        },
        type: 'label'
      }
    ],
    profileSection,
    [
      {
        label: t('userMenu.api'),
        icon: 'i-lucide-cloud',
        to: runtimeConfig.public.features.beta ? undefined : '/profile/integration/api-keys',
        disabled: runtimeConfig.public.features.beta,
        slot: 'api'
      }
    ],
    [
      {
        label: t('userMenu.logout'),
        icon: 'i-lucide-log-out',
        onSelect: () => authStore.logout()
      }
    ]
  ]
})
</script>

<template>
  <UDropdownMenu
    :items="items"
    :ui="{
      content: 'w-48'
    }"
  >
    <template #api="{ item }">
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-2">
          <UIcon
            :name="item.icon"
            class="w-4 h-4"
          />
          <span
            :class="[
              runtimeConfig.public.features.beta ? 'opacity-50' : ''
            ]"
          >
            {{ item.label }}
          </span>
        </div>
        <div
          v-if="runtimeConfig.public.features.beta"
          class="bg-orange-500 text-white text-[8px] px-1.5 py-0.5 rounded-sm"
        >
          {{ $t('Coming Soon') }}
        </div>
      </div>
    </template>

    <UUser
      :avatar="{
        icon: 'solar:user-bold-duotone'
      }"
      class="cursor-pointer"
      size="xl"
    />
  </UDropdownMenu>
</template>
