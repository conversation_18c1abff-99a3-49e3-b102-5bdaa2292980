<template>
  <UCard
    class="bg-neutral-50 dark:bg-neutral-800"
    :ui="{
      body: '!py-3'
    }"
  >
    <div class="flex flex-col gap-2">
      <div
        class="text-base font-semibold text-center flex items-center justify-center gap-2"
      >
        <UIcon
          name="i-simple-icons-paypal"
          class="size-6 text-yellow-600"
        />
        {{ $t("paypal") }}
      </div>
      <div class="text-sm text-center text-neutral-600 dark:text-neutral-400">
        {{ $t("paypalDescription") }}
      </div>
    </div>
    <div
      id="btnPaypal"
      class="mt-2"
    />
  </UCard>
</template>

<script setup lang="ts">
import { loadScript } from '@paypal/paypal-js'

const creditsStore = useCreditsStore()
const { loadings, quantityWanted, showDrawer, orderUUID } = storeToRefs(creditsStore)
const paypal = ref<any>(null)

// Component for PayPal payment option
const runtimeConfig = useRuntimeConfig()
const paypalId = runtimeConfig.public.NUXT_PAYPAL_ID
const router = useRouter()
const renderingButton = ref(false)
const renderPaypalButton = async () => {
  renderingButton.value = true
  if (paypal.value) {
    try {
      await paypal.value
        .Buttons({
          // Set up the transaction
          createOrder: async function (data: any, actions: any) {
            return creditsStore.createPaypalOrder({
              product_id: 'BC0001',
              quantity: quantityWanted.value
            })
          },

          // Finalize the transaction
          onApprove: async function (data, actions) {
            if (data?.paymentID) {
              const result = await creditsStore.approvePaypalOrder()
              if (result && result?.success) {
                // redirect to success page
                showDrawer.value = false
                router.push(
                  '/public/thank-you?payment=success&redirect=true&id='
                  + orderUUID.value
                )
              }
            } else {
              return await creditsStore.cancelPaypalOrder()
            }
          },

          onCancel: async function (data) {
            console.log('The payment was cancelled!', data)
            return creditsStore.cancelPaypalOrder()
          }
        })
        .render('#btnPaypal')
    } catch (error) {
      console.error('failed to render the PayPal Buttons', error)
    } finally {
      renderingButton.value = false
    }
  }
}

onMounted(async () => {
  try {
    paypal.value = await loadScript({
      clientId: paypalId,
      vault: true,
      disableFunding: 'card'
    })
  } catch (error) {
    console.error('failed to load the PayPal JS SDK script', error)
  }

  await renderPaypalButton()
})
</script>
