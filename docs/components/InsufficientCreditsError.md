# InsufficientCreditsError Component

## Overview
A reusable component for displaying insufficient credits errors across different generation types (video, speech, dialog, image).

## Usage

### Basic Usage (Video Generation)
```vue
<InsufficientCreditsError
  :credits-needed="actualCost"
  :available-credits="user_credit?.available_credit || 0"
  generation-type="video"
  @buy-credits="handleBuyCredits"
  @view-pricing="$router.push('/pricing')"
/>
```

### Speech Generation Example
```vue
<InsufficientCreditsError
  :credits-needed="
    Math.round(
      (getServicePriceByModelName(model?.value)?.effective_price || 0)
        * (inputText?.length || 1)
    )
  "
  :available-credits="user_credit?.available_credit || 0"
  generation-type="speech"
/>
```

### Dialog Generation Example
```vue
<InsufficientCreditsError
  :credits-needed="
    Math.round(
      (getServicePriceByModelName(model?.value)?.effective_price || 0)
        * dialogs.reduce((total, dialog) => total + (dialog.input?.length || 0), 0)
    )
  "
  :available-credits="user_credit?.available_credit || 0"
  generation-type="dialog"
/>
```

### Custom Handlers
```vue
<InsufficientCreditsError
  :credits-needed="100"
  :available-credits="50"
  generation-type="custom"
  title="Custom Title"
  message="Custom message for your specific use case"
  :on-buy-credits="() => customBuyCreditsHandler()"
  :on-view-pricing="() => customPricingHandler()"
  @buy-credits="handleCustomBuyCredits"
  @view-pricing="handleCustomPricing"
/>
```

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `creditsNeeded` | `number` | ✅ | - | Number of credits required for the generation |
| `availableCredits` | `number` | ❌ | `0` | User's current available credits |
| `generationType` | `string` | ❌ | `'generation'` | Type of generation (video, speech, dialog, image) |
| `title` | `string` | ❌ | Auto-generated | Custom title override |
| `message` | `string` | ❌ | Auto-generated | Custom message override |
| `onBuyCredits` | `function` | ❌ | Default handler | Custom buy credits function |
| `onViewPricing` | `function` | ❌ | Default handler | Custom view pricing function |
| `showPurchaseAmount` | `boolean` | ❌ | `true` | Show actual purchase amount section |
| `showMinimumNotice` | `boolean` | ❌ | `true` | Show minimum $10 purchase notice |

## Events

| Event | Description |
|-------|-------------|
| `buy-credits` | Emitted when buy credits button is clicked |
| `view-pricing` | Emitted when view pricing button is clicked |

## Features

### 🎨 Visual Design
- Attractive gradient coin icon
- Clean, centered layout
- Responsive design

### 💰 Smart Credit Calculation
- Shows credits needed vs available
- Calculates shortfall automatically
- Enforces minimum $10 purchase requirement
- Shows actual purchase amount when different from shortfall

### 🌍 Internationalization
- Dynamic messages based on generation type
- Fallback to generic messages
- Support for multiple languages

### 🔧 Customization
- Custom titles and messages
- Custom action handlers
- Configurable display options
- Type-specific styling

## Translation Keys

The component uses dynamic translation keys based on generation type:

### Base Keys
- `NOT_ENOUGH_CREDIT_TITLE`
- `BUY_CREDITS_NOW`
- `VIEW_PRICING`
- `MINIMUM_PURCHASE_10`

### Dynamic Keys (by type)
- `NOT_ENOUGH_CREDIT_MESSAGE_{TYPE}` (e.g., `NOT_ENOUGH_CREDIT_MESSAGE_VIDEO`)
- `{type}Gen.creditsNeeded` (e.g., `videoGen.creditsNeeded`)
- `{type}Gen.creditsAvailable`
- `{type}Gen.creditsShortfall`
- `{type}Gen.willPurchase`

### Fallback Keys
- `NOT_ENOUGH_CREDIT_MESSAGE`
- `creditsNeeded`
- `creditsAvailable`
- `creditsShortfall`
- `willPurchase`

## Integration Steps

1. **Add the component** to your error handling section
2. **Calculate credits needed** based on your service pricing
3. **Pass user's available credits** from auth store
4. **Specify generation type** for proper messaging
5. **Handle events** (optional) for custom behavior

## Benefits

✅ **Consistent UX** across all generation pages
✅ **Reduced code duplication**
✅ **Centralized credit logic**
✅ **Easy to maintain and update**
✅ **Type-safe and well-documented**
✅ **Automatic $10 minimum enforcement**
✅ **Responsive and accessible**
